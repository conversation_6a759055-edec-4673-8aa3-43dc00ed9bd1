/* Bootstrap CSS */
@import 'bootstrap/dist/css/bootstrap.css';

/* Font Awesome Icons */
@import '@fortawesome/fontawesome-free/css/all.css';

/* Custom Variables */
:root {
    --primary-color: #3B82F6;
    --secondary-color: #10B981;
    --text-dark: #1F2937;
    --text-light: #6B7280;
    --bg-light: #F9FAFB;
    --bg-white: #FFFFFF;
    --border-color: #E5E7EB;
    --success-color: #10B981;
    --error-color: #EF4444;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--bg-white);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

/* Utility Classes */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-light { background-color: var(--bg-light) !important; }

/* Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #2563EB;
    border-color: #2563EB;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    font-weight: 500;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: #059669;
    border-color: #059669;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Section Spacing */
.section {
    padding: 5rem 0;
}

.section-sm {
    padding: 3rem 0;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.75rem; }
    
    .section {
        padding: 3rem 0;
    }
    
    .btn-primary,
    .btn-secondary {
        padding: 0.625rem 1.5rem;
        font-size: 0.9rem;
    }
}
